package com.lysjk.utils;

import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Arrays;
import java.util.List;

/**
 * 统一的日期时间处理工具类
 * 提供灵活的日期格式解析和转换功能，支持多种常见的日期格式
 * 
 * <AUTHOR>
 */
@Slf4j
public class DateTimeUtil {

    /**
     * 支持的日期时间格式列表（按优先级排序）
     * 优先匹配更精确的格式
     */
    private static final List<DateTimeFormatter> SUPPORTED_DATETIME_FORMATTERS = Arrays.asList(
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"),
            DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm"),
            DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss"),
            DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm"),
            DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss"),
            DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm")
    );

    /**
     * 支持的日期格式列表
     */
    private static final List<DateTimeFormatter> SUPPORTED_DATE_FORMATTERS = Arrays.asList(
            DateTimeFormatter.ofPattern("yyyy-MM-dd"),
            DateTimeFormatter.ofPattern("yyyy/MM/dd"),
            DateTimeFormatter.ofPattern("dd/MM/yyyy"),
            DateTimeFormatter.ofPattern("dd-MM-yyyy")
    );

    /**
     * 常用的日期时间格式常量
     */
    public static final String DATETIME_PATTERN = "yyyy-MM-dd HH:mm:ss";
    public static final String DATETIME_MINUTE_PATTERN = "yyyy-MM-dd HH:mm";
    public static final String DATE_PATTERN = "yyyy-MM-dd";

    /**
     * 常用的格式化器
     */
    public static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern(DATETIME_PATTERN);
    public static final DateTimeFormatter DATETIME_MINUTE_FORMATTER = DateTimeFormatter.ofPattern(DATETIME_MINUTE_PATTERN);
    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(DATE_PATTERN);

    /**
     * 智能解析日期时间字符串
     * 自动识别多种常见的日期时间格式
     *
     * @param dateTimeStr 日期时间字符串
     * @return LocalDateTime对象，解析失败返回null
     */
    public static LocalDateTime parseDateTime(String dateTimeStr) {
        if (dateTimeStr == null || dateTimeStr.trim().isEmpty()) {
            return null;
        }

        String trimmedStr = dateTimeStr.trim();

        // 首先尝试解析完整的日期时间格式
        for (DateTimeFormatter formatter : SUPPORTED_DATETIME_FORMATTERS) {
            try {
                LocalDateTime result = LocalDateTime.parse(trimmedStr, formatter);
                log.debug("成功解析日期时间: {} -> {}", dateTimeStr, result);
                return result;
            } catch (DateTimeParseException e) {
                // 继续尝试下一个格式
                continue;
            }
        }

        // 如果日期时间格式都失败了，尝试只解析日期，然后补充时间为00:00:00
        for (DateTimeFormatter formatter : SUPPORTED_DATE_FORMATTERS) {
            try {
                LocalDate date = LocalDate.parse(trimmedStr, formatter);
                LocalDateTime result = date.atStartOfDay(); // 设置时间为00:00:00
                log.debug("成功解析日期并补充时间: {} -> {}", dateTimeStr, result);
                return result;
            } catch (DateTimeParseException e) {
                // 继续尝试下一个格式
                continue;
            }
        }

        log.warn("无法解析日期时间字符串: {}", dateTimeStr);
        return null;
    }

    /**
     * 智能解析日期时间字符串，解析失败时返回当前时间
     * 
     * @param dateTimeStr 日期时间字符串
     * @return LocalDateTime对象，解析失败返回当前时间
     */
    public static LocalDateTime parseDateTimeOrNow(String dateTimeStr) {
        LocalDateTime result = parseDateTime(dateTimeStr);
        if (result == null) {
            result = LocalDateTime.now();
            log.info("日期时间解析失败，使用当前时间: {}", result);
        }
        return result;
    }

    /**
     * 处理监测时间字段
     * 如果传入的时间为null或解析失败，则使用当前时间
     * 
     * @param monitoringDt 监测时间
     * @return 处理后的监测时间
     */
    public static LocalDateTime processMonitoringDateTime(LocalDateTime monitoringDt) {
        if (monitoringDt == null) {
            LocalDateTime now = LocalDateTime.now();
            log.debug("监测时间为空，使用当前时间: {}", now);
            return now;
        }
        return monitoringDt;
    }

    /**
     * 处理采样时间字段
     * 如果传入的时间为null或解析失败，则使用当前时间
     * 
     * @param sampleDt 采样时间
     * @return 处理后的采样时间
     */
    public static LocalDateTime processSampleDateTime(LocalDateTime sampleDt) {
        if (sampleDt == null) {
            LocalDateTime now = LocalDateTime.now();
            log.debug("采样时间为空，使用当前时间: {}", now);
            return now;
        }
        return sampleDt;
    }

    /**
     * 处理获取时间字段
     * 如果传入的时间为null或解析失败，则使用当前时间
     * 
     * @param acquisitionDt 获取时间
     * @return 处理后的获取时间
     */
    public static LocalDateTime processAcquisitionDateTime(LocalDateTime acquisitionDt) {
        if (acquisitionDt == null) {
            LocalDateTime now = LocalDateTime.now();
            log.debug("获取时间为空，使用当前时间: {}", now);
            return now;
        }
        return acquisitionDt;
    }

    /**
     * 格式化日期时间为标准格式字符串
     * 
     * @param dateTime 日期时间对象
     * @return 格式化后的字符串 (yyyy-MM-dd HH:mm:ss)
     */
    public static String formatDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.format(DATETIME_FORMATTER);
    }

    /**
     * 格式化日期时间为分钟精度字符串
     * 
     * @param dateTime 日期时间对象
     * @return 格式化后的字符串 (yyyy-MM-dd HH:mm)
     */
    public static String formatDateTimeMinute(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.format(DATETIME_MINUTE_FORMATTER);
    }

    /**
     * 格式化日期为日期字符串
     * 
     * @param dateTime 日期时间对象
     * @return 格式化后的字符串 (yyyy-MM-dd)
     */
    public static String formatDate(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.format(DATE_FORMATTER);
    }

    /**
     * 使用指定格式格式化日期时间
     * 
     * @param dateTime 日期时间对象
     * @param pattern 格式模式
     * @return 格式化后的字符串
     */
    public static String formatDateTime(LocalDateTime dateTime, String pattern) {
        if (dateTime == null || pattern == null || pattern.trim().isEmpty()) {
            return null;
        }
        
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
            return dateTime.format(formatter);
        } catch (Exception e) {
            log.error("格式化日期时间失败: dateTime={}, pattern={}", dateTime, pattern, e);
            return null;
        }
    }

    /**
     * 验证日期时间字符串是否符合指定格式
     * 
     * @param dateTimeStr 日期时间字符串
     * @param pattern 格式模式
     * @return 是否符合格式
     */
    public static boolean isValidDateTime(String dateTimeStr, String pattern) {
        if (dateTimeStr == null || dateTimeStr.trim().isEmpty() || 
            pattern == null || pattern.trim().isEmpty()) {
            return false;
        }

        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
            LocalDateTime.parse(dateTimeStr.trim(), formatter);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取当前时间的标准格式字符串
     * 
     * @return 当前时间字符串 (yyyy-MM-dd HH:mm:ss)
     */
    public static String getCurrentDateTimeString() {
        return LocalDateTime.now().format(DATETIME_FORMATTER);
    }

    /**
     * 获取当前日期的标准格式字符串
     * 
     * @return 当前日期字符串 (yyyy-MM-dd)
     */
    public static String getCurrentDateString() {
        return LocalDateTime.now().format(DATE_FORMATTER);
    }
}
