package com.lysjk.utils;

import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.*;
import org.locationtech.jts.io.ParseException;
import org.locationtech.jts.io.WKTReader;
import org.locationtech.jts.io.WKTWriter;

/**
 * PostGIS几何工具类
 * 用于处理Point和MultiPolygon等几何对象
 * 所有几何操作基于SRID 4326 (WGS84)，使用经纬度坐标系
 * 不支持3D坐标处理，仅处理2D (x=longitude, y=latitude)
 */
@Slf4j
public class GeometryUtil {

    private static final int SRID = 4326;
    private static final GeometryFactory GEOMETRY_FACTORY = new GeometryFactory(new PrecisionModel(), SRID);
    private static final WKTReader WKT_READER = new WKTReader(GEOMETRY_FACTORY);
    private static final WKTWriter WKT_WRITER = new WKTWriter(2); // 指定2D输出

    /**
     * 获取几何工厂实例
     * @return GeometryFactory实例
     */
    public static GeometryFactory getGeometryFactory() {
        return GEOMETRY_FACTORY;
    }

    /**
     * 从WKT字符串创建Point对象
     * @param wkt WKT字符串，如："POINT(118.77 32.04)"
     * @return Point对象
     */
    public static Point createPointFromWKT(String wkt) {
        if (wkt == null || wkt.trim().isEmpty()) {
            return null;
        }

        try {
            Geometry geometry = WKT_READER.read(wkt);
            if (geometry instanceof Point) {
                geometry.setSRID(SRID); // 确保SRID一致
                return (Point) geometry;
            } else {
                throw new IllegalArgumentException("WKT字符串不是有效的POINT类型: " + wkt);
            }
        } catch (ParseException e) {
            log.error("解析Point WKT失败: {}", wkt, e);
            throw new IllegalArgumentException("无效的Point WKT格式: " + wkt, e);
        }
    }

    /**
     * 从坐标创建Point对象
     * @param longitude 经度
     * @param latitude 纬度
     * @return Point对象
     */
    public static Point createPoint(double longitude, double latitude) {
        Coordinate coordinate = new Coordinate(longitude, latitude);
        Point point = GEOMETRY_FACTORY.createPoint(coordinate);
        point.setSRID(SRID); // 显式设置SRID
        return point;
    }

    /**
     * 从WKT字符串创建Polygon对象
     * @param wkt WKT字符串，如："POLYGON((118.76 32.03, 118.78 32.03, 118.78 32.05, 118.76 32.05, 118.76 32.03))"
     * @return Polygon对象
     */
    public static Polygon createPolygonFromWKT(String wkt) {
        if (wkt == null || wkt.trim().isEmpty()) {
            return null;
        }

        try {
            Geometry geometry = WKT_READER.read(wkt);
            if (geometry instanceof Polygon) {
                geometry.setSRID(SRID);
                return (Polygon) geometry;
            } else {
                throw new IllegalArgumentException("WKT字符串不是有效的POLYGON类型: " + wkt);
            }
        } catch (ParseException e) {
            log.error("解析Polygon WKT失败: {}", wkt, e);
            throw new IllegalArgumentException("无效的Polygon WKT格式: " + wkt, e);
        }
    }

    /**
     * 从WKT字符串创建MultiPolygon对象
     * @param wkt WKT字符串，如："MULTIPOLYGON(((118.76 32.03, 118.78 32.03, 118.78 32.05, 118.76 32.05, 118.76 32.03)))"
     * @return MultiPolygon对象
     */
    public static MultiPolygon createMultiPolygonFromWKT(String wkt) {
        if (wkt == null || wkt.trim().isEmpty()) {
            return null;
        }

        try {
            Geometry geometry = WKT_READER.read(wkt);
            if (geometry instanceof MultiPolygon) {
                geometry.setSRID(SRID);
                return (MultiPolygon) geometry;
            } else if (geometry instanceof Polygon) {
                // 如果是单个Polygon，转换为MultiPolygon
                Polygon[] polygons = new Polygon[]{(Polygon) geometry};
                MultiPolygon multiPolygon = GEOMETRY_FACTORY.createMultiPolygon(polygons);
                multiPolygon.setSRID(SRID);
                return multiPolygon;
            } else {
                throw new IllegalArgumentException("WKT字符串不是有效的POLYGON或MULTIPOLYGON类型: " + wkt);
            }
        } catch (ParseException e) {
            log.error("解析MultiPolygon WKT失败: {}", wkt, e);
            throw new IllegalArgumentException("无效的MultiPolygon WKT格式: " + wkt, e);
        }
    }

    /**
     * 从坐标数组创建简单的矩形MultiPolygon
     * 注意：在经纬度坐标系中，这是一个近似矩形（对于小区域有效）
     * @param minLon 最小经度
     * @param minLat 最小纬度
     * @param maxLon 最大经度
     * @param maxLat 最大纬度
     * @return MultiPolygon对象
     */
    public static MultiPolygon createRectangleMultiPolygon(double minLon, double minLat, double maxLon, double maxLat) {
        if (minLon >= maxLon || minLat >= maxLat) {
            throw new IllegalArgumentException("无效的矩形边界：min必须小于max");
        }

        Coordinate[] coordinates = new Coordinate[]{
                new Coordinate(minLon, minLat),
                new Coordinate(maxLon, minLat),
                new Coordinate(maxLon, maxLat),
                new Coordinate(minLon, maxLat),
                new Coordinate(minLon, minLat) // 闭合
        };

        LinearRing shell = GEOMETRY_FACTORY.createLinearRing(coordinates);
        Polygon polygon = GEOMETRY_FACTORY.createPolygon(shell);
        Polygon[] polygons = new Polygon[]{polygon};
        MultiPolygon multiPolygon = GEOMETRY_FACTORY.createMultiPolygon(polygons);
        multiPolygon.setSRID(SRID);
        return multiPolygon;
    }

    /**
     * 将Point对象转换为WKT字符串
     * @param point Point对象
     * @return WKT字符串
     */
    public static String pointToWKT(Point point) {
        if (point == null) {
            return null;
        }
        return WKT_WRITER.write(point);
    }

    /**
     * 将MultiPolygon对象转换为WKT字符串
     * @param multiPolygon MultiPolygon对象
     * @return WKT字符串
     */
    public static String multiPolygonToWKT(MultiPolygon multiPolygon) {
        if (multiPolygon == null) {
            return null;
        }
        return WKT_WRITER.write(multiPolygon);
    }

    /**
     * 验证WKT字符串是否为有效的Point格式
     * @param wkt WKT字符串
     * @return 是否有效
     */
    public static boolean isValidPointWKT(String wkt) {
        if (wkt == null || wkt.trim().isEmpty()) {
            return false;
        }

        try {
            Geometry geometry = WKT_READER.read(wkt);
            return geometry instanceof Point && !geometry.isEmpty();
        } catch (ParseException e) {
            return false;
        }
    }

    /**
     * 验证WKT字符串是否为有效的MultiPolygon格式
     * @param wkt WKT字符串
     * @return 是否有效
     */
    public static boolean isValidMultiPolygonWKT(String wkt) {
        if (wkt == null || wkt.trim().isEmpty()) {
            return false;
        }

        try {
            Geometry geometry = WKT_READER.read(wkt);
            return (geometry instanceof MultiPolygon || geometry instanceof Polygon) && !geometry.isEmpty();
        } catch (ParseException e) {
            return false;
        }
    }

    /**
     * 获取Point的经度
     * @param point Point对象
     * @return 经度
     */
    public static double getLongitude(Point point) {
        if (point == null) {
            throw new IllegalArgumentException("Point对象不能为空");
        }
        return point.getX();
    }

    /**
     * 获取Point的纬度
     * @param point Point对象
     * @return 纬度
     */
    public static double getLatitude(Point point) {
        if (point == null) {
            throw new IllegalArgumentException("Point对象不能为空");
        }
        return point.getY();
    }

    /**
     * 计算MultiPolygon的中心点
     * @param multiPolygon MultiPolygon对象
     * @return 中心点
     */
    public static Point getCentroid(MultiPolygon multiPolygon) {
        if (multiPolygon == null) {
            throw new IllegalArgumentException("MultiPolygon对象不能为空");
        }
        Point centroid = multiPolygon.getCentroid();
        centroid.setSRID(SRID);
        return centroid;
    }

    /**
     * 计算两个Point之间的距离（单位：米，使用Haversine公式，适合地理坐标）
     * 注意：这不是欧几里得距离，而是球面距离，假设地球半径为6371000米
     * @param point1 第一个点
     * @param point2 第二个点
     * @return 距离（米）
     */
    public static double distance(Point point1, Point point2) {
        if (point1 == null || point2 == null) {
            throw new IllegalArgumentException("Point对象不能为空");
        }
        if (point1.getSRID() != SRID || point2.getSRID() != SRID) {
            throw new IllegalArgumentException("Point必须使用SRID 4326");
        }

        double lat1 = Math.toRadians(point1.getY());
        double lon1 = Math.toRadians(point1.getX());
        double lat2 = Math.toRadians(point2.getY());
        double lon2 = Math.toRadians(point2.getX());

        double dLat = lat2 - lat1;
        double dLon = lon2 - lon1;

        double a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                Math.cos(lat1) * Math.cos(lat2) *
                        Math.sin(dLon / 2) * Math.sin(dLon / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        double radius = 6371000; // 地球平均半径（米）

        return c * radius;
    }
}

//package com.lysjk.utils;
//
//import lombok.extern.slf4j.Slf4j;
//import org.locationtech.jts.geom.*;
//import org.locationtech.jts.io.ParseException;
//import org.locationtech.jts.io.WKTReader;
//import org.locationtech.jts.io.WKTWriter;
//
///**
// * PostGIS几何工具类
// * 用于处理Point和MultiPolygon等几何对象
// */
//@Slf4j
//public class GeometryUtil {
//
//    private static final GeometryFactory GEOMETRY_FACTORY = new GeometryFactory(new PrecisionModel(), 4326);
//    private static final WKTReader WKT_READER = new WKTReader(GEOMETRY_FACTORY);
//    private static final WKTWriter WKT_WRITER = new WKTWriter();
//
//    /**
//     * 获取几何工厂实例
//     * @return GeometryFactory实例
//     */
//    public static GeometryFactory getGeometryFactory() {
//        return GEOMETRY_FACTORY;
//    }
//
//    /**
//     * 从WKT字符串创建Point对象
//     * @param wkt WKT字符串，如："POINT(118.77 32.04)"
//     * @return Point对象
//     */
//    public static Point createPointFromWKT(String wkt) {
//        if (wkt == null || wkt.trim().isEmpty()) {
//            return null;
//        }
//
//        try {
//            Geometry geometry = WKT_READER.read(wkt);
//            if (geometry instanceof Point) {
//                return (Point) geometry;
//            } else {
//                throw new IllegalArgumentException("WKT字符串不是有效的POINT类型: " + wkt);
//            }
//        } catch (ParseException e) {
//            log.error("解析Point WKT失败: {}", wkt, e);
//            throw new IllegalArgumentException("无效的Point WKT格式: " + wkt, e);
//        }
//    }
//
//    /**
//     * 从坐标创建Point对象
//     * @param longitude 经度
//     * @param latitude 纬度
//     * @return Point对象
//     */
//    public static Point createPoint(double longitude, double latitude) {
//        Coordinate coordinate = new Coordinate(longitude, latitude);
//        return GEOMETRY_FACTORY.createPoint(coordinate);
//    }
//
//    /**
//     * 从WKT字符串创建MultiPolygon对象
//     * @param wkt WKT字符串，如："MULTIPOLYGON(((118.76 32.03, 118.78 32.03, 118.78 32.05, 118.76 32.05, 118.76 32.03)))"
//     * @return MultiPolygon对象
//     */
//    public static MultiPolygon createMultiPolygonFromWKT(String wkt) {
//        if (wkt == null || wkt.trim().isEmpty()) {
//            return null;
//        }
//
//        try {
//            Geometry geometry = WKT_READER.read(wkt);
//            if (geometry instanceof MultiPolygon) {
//                return (MultiPolygon) geometry;
//            } else if (geometry instanceof Polygon) {
//                // 如果是单个Polygon，转换为MultiPolygon
//                Polygon[] polygons = new Polygon[]{(Polygon) geometry};
//                return GEOMETRY_FACTORY.createMultiPolygon(polygons);
//            } else {
//                throw new IllegalArgumentException("WKT字符串不是有效的POLYGON或MULTIPOLYGON类型: " + wkt);
//            }
//        } catch (ParseException e) {
//            log.error("解析MultiPolygon WKT失败: {}", wkt, e);
//            throw new IllegalArgumentException("无效的MultiPolygon WKT格式: " + wkt, e);
//        }
//    }
//
//    /**
//     * 从坐标数组创建简单的矩形MultiPolygon
//     * @param minLon 最小经度
//     * @param minLat 最小纬度
//     * @param maxLon 最大经度
//     * @param maxLat 最大纬度
//     * @return MultiPolygon对象
//     */
//    public static MultiPolygon createRectangleMultiPolygon(double minLon, double minLat, double maxLon, double maxLat) {
//        Coordinate[] coordinates = new Coordinate[]{
//            new Coordinate(minLon, minLat),
//            new Coordinate(maxLon, minLat),
//            new Coordinate(maxLon, maxLat),
//            new Coordinate(minLon, maxLat),
//            new Coordinate(minLon, minLat) // 闭合
//        };
//
//        LinearRing shell = GEOMETRY_FACTORY.createLinearRing(coordinates);
//        Polygon polygon = GEOMETRY_FACTORY.createPolygon(shell);
//        Polygon[] polygons = new Polygon[]{polygon};
//
//        return GEOMETRY_FACTORY.createMultiPolygon(polygons);
//    }
//
//    /**
//     * 将Point对象转换为WKT字符串
//     * @param point Point对象
//     * @return WKT字符串
//     */
//    public static String pointToWKT(Point point) {
//        if (point == null) {
//            return null;
//        }
//        return WKT_WRITER.write(point);
//    }
//
//    /**
//     * 将MultiPolygon对象转换为WKT字符串
//     * @param multiPolygon MultiPolygon对象
//     * @return WKT字符串
//     */
//    public static String multiPolygonToWKT(MultiPolygon multiPolygon) {
//        if (multiPolygon == null) {
//            return null;
//        }
//        return WKT_WRITER.write(multiPolygon);
//    }
//
//    /**
//     * 验证WKT字符串是否为有效的Point格式
//     * @param wkt WKT字符串
//     * @return 是否有效
//     */
//    public static boolean isValidPointWKT(String wkt) {
//        if (wkt == null || wkt.trim().isEmpty()) {
//            return false;
//        }
//
//        try {
//            Geometry geometry = WKT_READER.read(wkt);
//            return geometry instanceof Point;
//        } catch (ParseException e) {
//            return false;
//        }
//    }
//
//    /**
//     * 验证WKT字符串是否为有效的MultiPolygon格式
//     * @param wkt WKT字符串
//     * @return 是否有效
//     */
//    public static boolean isValidMultiPolygonWKT(String wkt) {
//        if (wkt == null || wkt.trim().isEmpty()) {
//            return false;
//        }
//
//        try {
//            Geometry geometry = WKT_READER.read(wkt);
//            return geometry instanceof MultiPolygon || geometry instanceof Polygon;
//        } catch (ParseException e) {
//            return false;
//        }
//    }
//
//    /**
//     * 获取Point的经度
//     * @param point Point对象
//     * @return 经度
//     */
//    public static double getLongitude(Point point) {
//        if (point == null) {
//            throw new IllegalArgumentException("Point对象不能为空");
//        }
//        return point.getX();
//    }
//
//    /**
//     * 获取Point的纬度
//     * @param point Point对象
//     * @return 纬度
//     */
//    public static double getLatitude(Point point) {
//        if (point == null) {
//            throw new IllegalArgumentException("Point对象不能为空");
//        }
//        return point.getY();
//    }
//
//    /**
//     * 计算MultiPolygon的中心点
//     * @param multiPolygon MultiPolygon对象
//     * @return 中心点
//     */
//    public static Point getCentroid(MultiPolygon multiPolygon) {
//        if (multiPolygon == null) {
//            throw new IllegalArgumentException("MultiPolygon对象不能为空");
//        }
//        return multiPolygon.getCentroid();
//    }
//
//    /**
//     * 计算两个Point之间的距离（单位：度）
//     * @param point1 第一个点
//     * @param point2 第二个点
//     * @return 距离
//     */
//    public static double distance(Point point1, Point point2) {
//        if (point1 == null || point2 == null) {
//            throw new IllegalArgumentException("Point对象不能为空");
//        }
//        return point1.distance(point2);
//    }
//}
