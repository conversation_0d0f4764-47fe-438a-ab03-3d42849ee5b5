package com.lysjk.config.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.Polygon;
import org.locationtech.jts.io.WKTWriter;

import java.io.IOException;

/**
 * Polygon类型的JSON序列化器
 * 将Polygon对象序列化为包含type、wkt、srid和coordinates的对象
 * coordinates是GeoJSON-like的嵌套数组，便于前端解析
 */
public class PolygonSerializer extends JsonSerializer<Polygon> {

    private static final WKTWriter WKT_WRITER = new WKTWriter();

    @Override
    public void serialize(Polygon polygon, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (polygon == null) {
            gen.writeNull();
            return;
        }

        gen.writeStartObject();
        gen.writeStringField("type", "Polygon");
        gen.writeStringField("wkt", WKT_WRITER.write(polygon));
        if (polygon.getSRID() != 0) {
            gen.writeNumberField("srid", polygon.getSRID());
        }

        // 写入坐标数组（经纬度）
        gen.writeFieldName("coordinates");
        gen.writeStartArray();

        // 外环坐标
        writeRingCoordinates(gen, polygon.getExteriorRing().getCoordinates());

        // 内环坐标
        for (int i = 0; i < polygon.getNumInteriorRing(); i++) {
            writeRingCoordinates(gen, polygon.getInteriorRingN(i).getCoordinates());
        }

        gen.writeEndArray();
        gen.writeEndObject();
    }

    private void writeRingCoordinates(JsonGenerator gen, Coordinate[] coordinates) throws IOException {
        gen.writeStartArray();
        for (Coordinate coord : coordinates) {
            gen.writeStartArray();
            gen.writeNumber(coord.x); // longitude
            gen.writeNumber(coord.y); // latitude
            gen.writeEndArray();
        }
        gen.writeEndArray();
    }
}
