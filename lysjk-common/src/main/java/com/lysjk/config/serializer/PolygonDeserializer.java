package com.lysjk.config.serializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.lysjk.utils.GeometryUtil;
import org.locationtech.jts.geom.*;
import org.locationtech.jts.io.WKTReader;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * Polygon类型的JSON反序列化器
 * 支持WKT字符串、对象中的wkt字段，或GeoJSON-like的coordinates数组
 */
public class PolygonDeserializer extends JsonDeserializer<Polygon> {

    private static final GeometryFactory GEOMETRY_FACTORY = new GeometryFactory();
    private static final WKTReader WKT_READER = new WKTReader();

    @Override
    public Polygon deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        JsonNode node = p.getCodec().readTree(p);

        if (node.isNull()) {
            return null;
        }

        // 如果是字符串，尝试解析为WKT
        if (node.isTextual()) {
            String wkt = node.asText();
            return GeometryUtil.createPolygonFromWKT(wkt);
        }

        // 如果是对象，从WKT字段创建Polygon
        if (node.isObject()) {
            if (node.has("wkt")) {
                String wkt = node.get("wkt").asText();
                return GeometryUtil.createPolygonFromWKT(wkt);
            } else if (node.has("coordinates")) {
                // 支持从coordinates数组解析（GeoJSON风格）
                return createPolygonFromCoordinates(node.get("coordinates"));
            }
        }

        throw new IOException("无法解析Polygon对象，预期格式：WKT字符串或包含'wkt'/'coordinates'的对象: " + node.toString());
    }

    private Polygon createPolygonFromCoordinates(JsonNode coordinatesNode) throws IOException {
        if (!coordinatesNode.isArray()) {
            throw new IOException("coordinates必须是数组");
        }

        // 外环
        JsonNode exteriorNode = coordinatesNode.get(0);
        LinearRing exterior = createRingFromArray(exteriorNode);

        // 内环
        List<LinearRing> interiors = new ArrayList<>();
        for (int i = 1; i < coordinatesNode.size(); i++) {
            interiors.add(createRingFromArray(coordinatesNode.get(i)));
        }

        return GEOMETRY_FACTORY.createPolygon(exterior, interiors.toArray(new LinearRing[0]));
    }

    private LinearRing createRingFromArray(JsonNode ringNode) throws IOException {
        if (!ringNode.isArray()) {
            throw new IOException("环坐标必须是数组");
        }

        List<Coordinate> coords = new ArrayList<>();
        for (JsonNode coordNode : ringNode) {
            if (coordNode.isArray() && coordNode.size() >= 2) {
                double x = coordNode.get(0).asDouble();
                double y = coordNode.get(1).asDouble();
                coords.add(new Coordinate(x, y));
            }
        }

        // 确保闭合
        if (!coords.isEmpty() && !coords.get(0).equals(coords.get(coords.size() - 1))) {
            coords.add(coords.get(0));
        }

        return GEOMETRY_FACTORY.createLinearRing(coords.toArray(new Coordinate[0]));
    }
}
