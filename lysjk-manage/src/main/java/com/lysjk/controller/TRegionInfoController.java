package com.lysjk.controller;

import com.lysjk.anno.LogOperation;
import com.lysjk.dto.TRegionInfoPageQueryDTO;
import com.lysjk.dto.TRegionInfoWithMonitoringPointsDTO;
import com.lysjk.entity.TRegionInfo;
import com.lysjk.exception.ValidationException;
import com.lysjk.result.PageResult;
import com.lysjk.result.Result;
import com.lysjk.service.TRegionInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 地物信息controller层
 */
@Slf4j
@RestController
@RequestMapping("/regionInfo")
public class TRegionInfoController {

    @Autowired
    private TRegionInfoService tRegionInfoService;

    /**
     * 新增地物信息
     * @param regionInfo 地物信息
     * @return 操作结果
     */
    @PostMapping
    @LogOperation(operationName = "新增地物信息")
    public Result save(@RequestBody TRegionInfo regionInfo) {
        log.info("新增地物信息: {}", regionInfo);
        tRegionInfoService.save(regionInfo);
        return Result.success();
    }

    /**
     * 批量删除地物信息
     * 批量删除格式为?ids=1,2,3
     * @param ids ID列表
     * @return 操作结果
     */
    @DeleteMapping
    @LogOperation(operationName = "删除地物信息")
    public Result delete(@RequestParam List<Integer> ids) {
        log.info("批量删除地物信息: {}", ids);
        tRegionInfoService.deleteBatch(ids);
        return Result.success();
    }

    /**
     * 更新地物信息
     * 必传id,然后是json格式
     * @param regionInfo 地物信息
     * @return 操作结果
     */
    @PutMapping
    @LogOperation(operationName = "更新地物信息")
    public Result update(@RequestBody TRegionInfo regionInfo) {
        log.info("更新地物信息: {}", regionInfo);
        tRegionInfoService.update(regionInfo);
        return Result.success();
    }

    /**
     * 根据主键查询地物信息
     */
    @LogOperation(operationName = "查询地物信息")
    @GetMapping("/{id}")
    public Result<TRegionInfo> selectById(@PathVariable Integer id) {
        if (id == null) {
            throw new ValidationException.ParameterNullException("地物ID");
        }
        TRegionInfo regionInfo = tRegionInfoService.selectByPrimaryKey(id);
        if (regionInfo != null) {
            return Result.success(regionInfo);
        } else {
            throw new ValidationException.DataNotFoundException("地物信息", id.toString());
        }
    }

    /**
     * 分页条件查询地物信息
     * 可以根据多个条件进行分页查询
     * @param pageQueryDTO 分页查询条件
     * @return 分页结果
     */
    @GetMapping("/page")
    @LogOperation(operationName = "分页查询地物信息")
    public Result<PageResult> selectPage(TRegionInfoPageQueryDTO pageQueryDTO) {
        log.info("分页查询地物信息: {}", pageQueryDTO);
        PageResult pageResult = tRegionInfoService.selectPage(pageQueryDTO);
        return Result.success(pageResult);
    }

    /**
     * 查询地物信息及其关联的监测点列表
     */
    @LogOperation(operationName = "查询地物信息及关联监测点")
    @GetMapping("/withMonitoringPoints")
    public Result<List<TRegionInfoWithMonitoringPointsDTO>> selectRegionInfoWithMonitoringPoints() {
        List<TRegionInfoWithMonitoringPointsDTO> result = tRegionInfoService.selectRegionInfoWithMonitoringPoints();
        return Result.success(result);
    }

    /**
     * 根据地物ID查询地物信息及其关联的监测点列表
     */
    @LogOperation(operationName = "查询指定地物信息及关联监测点")
    @GetMapping("/{id}/withMonitoringPoints")
    public Result<List<TRegionInfoWithMonitoringPointsDTO>> selectRegionInfoWithMonitoringPointsById(@PathVariable Integer id) {
        if (id == null) {
            throw new ValidationException.ParameterNullException("地物ID");
        }
        List<TRegionInfoWithMonitoringPointsDTO> result = tRegionInfoService.selectRegionInfoWithMonitoringPointsById(id);
        if (result != null) {
            return Result.success(result);
        } else {
            throw new ValidationException.DataNotFoundException("地物信息", id.toString());
        }
    }
}
