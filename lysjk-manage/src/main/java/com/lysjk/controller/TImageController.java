package com.lysjk.controller;

import com.lysjk.anno.LogOperation;
import com.lysjk.dto.TImagePageQueryDTO;
import com.lysjk.entity.TImage;
import com.lysjk.exception.ValidationException;
import com.lysjk.result.PageResult;
import com.lysjk.result.Result;
import com.lysjk.service.TImageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 影像信息controller层
 */
@Slf4j
@RestController
@RequestMapping("/image")
public class TImageController {

    @Autowired
    private TImageService tImageService;

    /**
     * 新增影像信息
     * @param image 影像信息
     * @return 操作结果
     */
    @PostMapping
    @LogOperation(operationName = "新增影像信息")
    public Result save(@RequestBody TImage image) {
        log.info("新增影像信息: {}", image);
        tImageService.save(image);
        return Result.success();
    }

    /**
     * 批量删除影像信息
     * 批量删除格式为?ids=1,2,3
     * @param ids ID列表
     * @return 操作结果
     */
    @DeleteMapping
    @LogOperation(operationName = "删除影像信息")
    public Result delete(@RequestParam List<Integer> ids) {
        log.info("批量删除影像信息: {}", ids);
        tImageService.deleteBatch(ids);
        return Result.success();
    }

    /**
     * 更新影像信息
     * 必传id,然后是json格式
     * @param image 影像信息
     * @return 操作结果
     */
    @PutMapping
    @LogOperation(operationName = "更新影像信息")
    public Result update(@RequestBody TImage image) {
        log.info("更新影像信息: {}", image);
        tImageService.update(image);
        return Result.success();
    }

    /**
     * 根据主键查询影像信息
     */
    @LogOperation(operationName = "查询影像信息")
    @GetMapping("/{id}")
    public Result<TImage> selectById(@PathVariable Integer id) {
        if (id == null) {
            throw new ValidationException.ParameterNullException("影像ID");
        }
        TImage image = tImageService.selectByPrimaryKey(id);
        if (image != null) {
            return Result.success(image);
        } else {
            throw new ValidationException.DataNotFoundException("影像信息", id.toString());
        }
    }

    /**
     * 分页条件查询影像信息
     * 可以根据多个条件进行分页查询
     * @param pageQueryDTO 分页查询条件
     * @return 分页结果
     */
    @GetMapping("/page")
    @LogOperation(operationName = "分页查询影像信息")
    public Result<PageResult> selectPage(TImagePageQueryDTO pageQueryDTO) {
        log.info("分页查询影像信息: {}", pageQueryDTO);
        PageResult pageResult = tImageService.selectPage(pageQueryDTO);
        return Result.success(pageResult);
    }

    /**
     * 根据地物ID查询影像信息列表
     * @param regionId 地物ID
     * @return 影像信息列表
     */
    @GetMapping("/region/{regionId}")
    @LogOperation(operationName = "根据地物ID查询影像信息")
    public Result<List<TImage>> selectByRegionId(@PathVariable Integer regionId) {
        if (regionId == null) {
            throw new ValidationException.ParameterNullException("地物ID");
        }
        List<TImage> result = tImageService.selectByRegionId(regionId);
        return Result.success(result);
    }

    /**
     * 根据传感器类型查询影像信息列表
     * @param sensorType 传感器类型
     * @return 影像信息列表
     */
    @GetMapping("/sensor/{sensorType}")
    @LogOperation(operationName = "根据传感器类型查询影像信息")
    public Result<List<TImage>> selectBySensorType(@PathVariable String sensorType) {
        if (sensorType == null || sensorType.trim().isEmpty()) {
            throw new ValidationException.ParameterNullException("传感器类型");
        }
        List<TImage> result = tImageService.selectBySensorType(sensorType);
        return Result.success(result);
    }
}
