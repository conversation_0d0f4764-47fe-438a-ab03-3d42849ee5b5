package com.lysjk.service;

import com.lysjk.dto.TImagePageQueryDTO;
import com.lysjk.entity.TImage;
import com.lysjk.result.PageResult;

import java.util.List;

/**
 * 影像信息服务接口
 */
public interface TImageService {

    /**
     * 新增影像信息
     * @param image 影像信息
     */
    void save(TImage image);

    /**
     * 批量删除影像信息
     * @param ids ID列表
     */
    void deleteBatch(List<Integer> ids);

    /**
     * 更新影像信息
     * @param image 影像信息
     */
    void update(TImage image);

    /**
     * 根据主键查询影像信息
     * @param id 影像ID
     * @return 影像信息
     */
    TImage selectByPrimaryKey(Integer id);

    /**
     * 分页查询影像信息
     * @param pageQueryDTO 分页查询条件
     * @return 分页结果
     */
    PageResult selectPage(TImagePageQueryDTO pageQueryDTO);

    /**
     * 根据地物ID查询影像信息列表
     * @param regionId 地物ID
     * @return 影像信息列表
     */
    List<TImage> selectByRegionId(Integer regionId);

    /**
     * 根据传感器类型查询影像信息列表
     * @param sensorType 传感器类型
     * @return 影像信息列表
     */
    List<TImage> selectBySensorType(String sensorType);
}
