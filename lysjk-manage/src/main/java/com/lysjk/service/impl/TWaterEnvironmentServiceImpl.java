package com.lysjk.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.lysjk.constant.RoleConstant;
import com.lysjk.dto.TWaterEnvironmentPageQueryDTO;
import com.lysjk.entity.TWaterEnvironment;
import com.lysjk.exception.AuthenticationException;
import com.lysjk.exception.BusinessException;
import com.lysjk.exception.ValidationException;
import com.lysjk.mapper.TWaterEnvironmentMapper;
import com.lysjk.result.PageResult;
import com.lysjk.service.TWaterEnvironmentService;
import com.lysjk.utils.DateTimeUtil;
import com.lysjk.utils.ThreadLocalUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 水域环境信息服务实现类
 */
@Slf4j
@Service
@Transactional
public class TWaterEnvironmentServiceImpl implements TWaterEnvironmentService {

    @Autowired
    private TWaterEnvironmentMapper waterEnvironmentMapper;

    /**
     * 新增水域环境信息
     * @param waterEnvironment 水域环境信息
     */
    @Override
    public void save(TWaterEnvironment waterEnvironment) {
        if (waterEnvironment == null) {
            throw new ValidationException.ParameterNullException("水域环境信息");
        }

        // TODO: 验证必填字段,这个以后再想想吧
//        validateRequiredFields(waterEnvironment);

        // 处理监测时间（如果未设置则使用当前时间）
        waterEnvironment.setMonitoringDt(DateTimeUtil.processMonitoringDateTime(waterEnvironment.getMonitoringDt()));

        log.info("新增水域环境信息: pointId={}, regionId={}, monitoringDt={}",
                waterEnvironment.getPointId(), waterEnvironment.getRegionId(), waterEnvironment.getMonitoringDt());

        int result = waterEnvironmentMapper.insert(waterEnvironment);
        if (result <= 0) {
            throw new BusinessException.OperationFailedException("新增水域环境信息");
        }
    }

    /**
     * 批量删除水域环境信息
     * @param ids ID列表
     */
    @Override
    public void deleteBatch(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new ValidationException.ParameterNullException("删除ID列表");
        }

        // 权限验证
        Map<String, Object> claims = ThreadLocalUtil.get();
        String role = (String) claims.get(RoleConstant.ROLE);
        if (!RoleConstant.ADMIN.equals(role)) {
            throw new AuthenticationException.InsufficientPermissionException("删除水域环境信息");
        }

        log.info("批量删除水域环境信息: {}", ids);

        int result = waterEnvironmentMapper.deleteByIds(ids);
        if (result <= 0) {
            throw new BusinessException.OperationFailedException("删除水域环境信息");
        }
    }

    /**
     * 更新水域环境信息
     * @param waterEnvironment 水域环境信息
     */
    @Override
    public void update(TWaterEnvironment waterEnvironment) {
        if (waterEnvironment == null) {
            throw new ValidationException.ParameterNullException("水域环境信息");
        }

        if (waterEnvironment.getId() == null) {
            throw new ValidationException.ParameterNullException("水域环境信息ID");
        }

        log.info("更新水域环境信息: id={}, pointId={}, regionId={}",
                waterEnvironment.getId(), waterEnvironment.getPointId(), waterEnvironment.getRegionId());

        int result = waterEnvironmentMapper.update(waterEnvironment);
        if (result <= 0) {
            throw new BusinessException.OperationFailedException("更新水域环境信息");
        }
    }

    /**
     * 分页查询水域环境信息
     * @param pageQueryDTO 分页查询条件
     * @return 分页结果
     */
    @Override
    public PageResult selectPage(TWaterEnvironmentPageQueryDTO pageQueryDTO) {
        if (pageQueryDTO == null) {
            throw new ValidationException.ParameterNullException("分页查询条件");
        }

        log.info("分页查询水域环境信息: page={}, pageSize={}, pointId={}, regionId={}",
                pageQueryDTO.getPage(), pageQueryDTO.getPageSize(),
                pageQueryDTO.getPointId(), pageQueryDTO.getRegionId());

        // 开启分页
        PageHelper.startPage(pageQueryDTO.getPage(), pageQueryDTO.getPageSize());

        // 执行查询
        Page<TWaterEnvironment> page = waterEnvironmentMapper.pageQuery(pageQueryDTO);

        // 封装结果
        long total = page.getTotal();
        List<TWaterEnvironment> result = page.getResult();

        log.info("分页查询完成，共查询到{}条记录", total);
        return new PageResult(total, result);
    }

    /**
     * 验证必填字段
     * 暂时用不到
     */
/*    private void validateRequiredFields(TWaterEnvironment waterEnvironment) {
        if (waterEnvironment.getPointId() == null) {
            throw new ValidationException.ParameterNullException("监测点ID");
        }
        if (waterEnvironment.getRegionId() == null) {
            throw new ValidationException.ParameterNullException("所属地物ID");
        }
    }*/
}
