package com.lysjk.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.lysjk.constant.RoleConstant;
import com.lysjk.dto.TMonitoringPointPageQueryDTO;
import com.lysjk.entity.TMonitoringPoint;
import com.lysjk.exception.AuthenticationException;
import com.lysjk.exception.BusinessException;
import com.lysjk.exception.ValidationException;
import com.lysjk.mapper.TMonitoringPointMapper;
import com.lysjk.result.PageResult;
import com.lysjk.service.TMonitoringPointService;
import com.lysjk.utils.ThreadLocalUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 监测点信息服务实现类
 */
@Slf4j
@Service
@Transactional
public class TMonitoringPointServiceImpl implements TMonitoringPointService {

    @Autowired
    private TMonitoringPointMapper tMonitoringPointMapper;

    /**
     * 新增监测点信息
     * @param monitoringPoint 监测点信息
     */
    @Override
    public void save(TMonitoringPoint monitoringPoint) {
        if (monitoringPoint == null) {
            throw new ValidationException.ParameterNullException("监测点信息");
        }

        // 检查监测点编码唯一性
        if (monitoringPoint.getCode() != null && !monitoringPoint.getCode().trim().isEmpty()) {
            boolean isUnique = isCodeUnique(monitoringPoint.getCode(), null);
            if (!isUnique) {
                throw new ValidationException.DuplicateDataException("监测点编码", monitoringPoint.getCode());
            }
        }

        log.info("新增监测点信息: code={}, name={}", monitoringPoint.getCode(), monitoringPoint.getName());

        int result = tMonitoringPointMapper.insertSelective(monitoringPoint);
        if (result <= 0) {
            throw new BusinessException.OperationFailedException("新增监测点信息");
        }
    }

    /**
     * 批量删除监测点信息
     * @param ids ID列表
     */
    @Override
    public void deleteBatch(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new ValidationException.ParameterNullException("删除ID列表");
        }

        // 权限验证
        Map<String, Object> claims = ThreadLocalUtil.get();
        String role = (String) claims.get(RoleConstant.ROLE);
        if (!RoleConstant.ADMIN.equals(role)) {
            throw new AuthenticationException.InsufficientPermissionException("删除监测点信息");
        }

        log.info("批量删除监测点信息: {}", ids);

        int result = tMonitoringPointMapper.deleteByIds(ids);
        if (result <= 0) {
            throw new BusinessException.OperationFailedException("删除监测点信息");
        }
    }

    /**
     * 更新监测点信息
     * @param monitoringPoint 监测点信息
     */
    @Override
    public void update(TMonitoringPoint monitoringPoint) {
        if (monitoringPoint == null) {
            throw new ValidationException.ParameterNullException("监测点信息");
        }

        if (monitoringPoint.getId() == null) {
            throw new ValidationException.ParameterNullException("监测点信息ID");
        }

        // 检查监测点编码唯一性（排除当前记录）
        if (monitoringPoint.getCode() != null && !monitoringPoint.getCode().trim().isEmpty()) {
            boolean isUnique = isCodeUnique(monitoringPoint.getCode(), monitoringPoint.getId());
            if (!isUnique) {
                throw new ValidationException.DuplicateDataException("监测点编码", monitoringPoint.getCode());
            }
        }

        log.info("更新监测点信息: id={}, code={}, name={}",
                monitoringPoint.getId(), monitoringPoint.getCode(), monitoringPoint.getName());

        int result = tMonitoringPointMapper.updateBusinessFields(monitoringPoint);
        if (result <= 0) {
            throw new BusinessException.OperationFailedException("更新监测点信息");
        }
    }

    @Override
    public TMonitoringPoint selectByPrimaryKey(Integer id) {
        if (id == null) {
            throw new IllegalArgumentException("监测点ID不能为空");
        }
        log.info("根据ID查询监测点信息: {}", id);
        return tMonitoringPointMapper.selectByPrimaryKey(id);
    }

    /**
     * 分页条件查询监测点信息
     * @param pageQueryDTO 分页查询条件
     * @return 分页结果
     */
    @Override
    public PageResult selectPage(TMonitoringPointPageQueryDTO pageQueryDTO) {
        if (pageQueryDTO == null) {
            throw new ValidationException.ParameterNullException("分页查询条件");
        }

        log.info("分页查询监测点信息: page={}, pageSize={}, name={}, code={}",
                pageQueryDTO.getPage(), pageQueryDTO.getPageSize(),
                pageQueryDTO.getName(), pageQueryDTO.getCode());

        // 开启分页
        PageHelper.startPage(pageQueryDTO.getPage(), pageQueryDTO.getPageSize());

        // 执行查询
        Page<TMonitoringPoint> page = tMonitoringPointMapper.pageQuery(pageQueryDTO);

        // 封装结果
        long total = page.getTotal();
        List<TMonitoringPoint> result = page.getResult();

        log.info("分页查询完成，共查询到{}条记录", total);
        return new PageResult(total, result);
    }

    @Override
    public List<TMonitoringPoint> selectByRegionId(Integer regionId) {
        if (regionId == null) {
            throw new ValidationException.ParameterNullException("地物ID");
        }
        log.info("根据地物ID查询监测点: {}", regionId);
        return tMonitoringPointMapper.selectByRegionId(regionId);
    }

    /**
     * 检查监测点编码唯一性
     * @param code 监测点编码
     * @param excludeId 排除的ID（更新时使用）
     * @return 是否唯一
     */
    private boolean isCodeUnique(String code, Integer excludeId) {
        if (code == null || code.trim().isEmpty()) {
            return true; // 空编码认为是唯一的
        }

        log.info("检查监测点编码唯一性 - 编码:{}, 排除ID:{}", code, excludeId);
        TMonitoringPoint existing = tMonitoringPointMapper.selectByCodeExcludeId(code, excludeId);
        boolean isUnique = existing == null;

        log.info("编码唯一性检查结果: {}", isUnique ? "唯一" : "重复");
        return isUnique;
    }
}
