package com.lysjk.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.lysjk.constant.RoleConstant;
import com.lysjk.dto.TOrganizationInfoPageQueryDTO;
import com.lysjk.entity.TOrganizationInfo;
import com.lysjk.exception.AuthenticationException;
import com.lysjk.exception.ValidationException;
import com.lysjk.mapper.TOrganizationInfoMapper;
import com.lysjk.result.PageResult;
import com.lysjk.service.TOrganizationInfoService;
import com.lysjk.utils.ThreadLocalUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class TOrganizationInfoServiceImpl implements TOrganizationInfoService {

    @Autowired
    private TOrganizationInfoMapper organizationInfoMapper;

    /**
     * 新增单位
     * @param organizationInfo
     */
    @Override
    public void save(TOrganizationInfo organizationInfo) {
        if (organizationInfo == null) {
            throw new ValidationException.ParameterNullException("单位信息");
        }

        // 检查单位名称唯一性
        if (organizationInfo.getName() != null && !organizationInfo.getName().trim().isEmpty()) {
            checkNameUnique(organizationInfo.getName());
        }

        log.info("新增单位信息: name={}", organizationInfo.getName());
        organizationInfoMapper.insert(organizationInfo);
    }

    /**
     * 批量删除操作
     * @param ids
     */
    @Override
    @Transactional
    public void deleteBath(List<Long> ids) {
        // 先校验权限
        Map<String, Object> claims = ThreadLocalUtil.get();
        String role = (String) claims.get(RoleConstant.ROLE);
        if(role.equals(RoleConstant.ADMIN)){
            // TODO: 以后再完成查看其他表格是否关联看能否删除id,注意有异常直接抛,返回的也是Result方便前端提升信息
            organizationInfoMapper.deleteByIds(ids);
        }
        else throw new AuthenticationException.InsufficientPermissionException();
    }

    @Override
    public void update(TOrganizationInfo organizationInfo) {
        if (organizationInfo == null) {
            throw new ValidationException.ParameterNullException("单位信息");
        }

        if (organizationInfo.getId() == null) {
            throw new ValidationException.ParameterNullException("单位信息ID");
        }

        // 检查单位名称唯一性（排除当前记录）
        if (organizationInfo.getName() != null && !organizationInfo.getName().trim().isEmpty()) {
            boolean isUnique = isNameUnique(organizationInfo.getName(), organizationInfo.getId());
            if (!isUnique) {
                throw new ValidationException.DuplicateDataException("单位名称", organizationInfo.getName());
            }
        }

        log.info("更新单位信息: id={}, name={}", organizationInfo.getId(), organizationInfo.getName());
        organizationInfoMapper.update(organizationInfo);
    }

    /**
     * 根据名称条件分页查询
     * @param organizationInfoPageQueryDTO
     * @return
     */
    @Override
    public PageResult selectPage(TOrganizationInfoPageQueryDTO organizationInfoPageQueryDTO) {
        // 先开启分页
        PageHelper.startPage(organizationInfoPageQueryDTO.getPage(), organizationInfoPageQueryDTO.getPageSize());
        // 开始按条件查询
        Page<TOrganizationInfo> page = organizationInfoMapper.pageQuery(organizationInfoPageQueryDTO);
        // 封装结果并返回
        long total = page.getTotal();
        List<TOrganizationInfo> result = page.getResult();
        return new PageResult(total, result);
    }

    /**
     * 检查单位名称唯一性
     * @param name 单位名称
     * @param excludeId 排除的ID（更新时使用，新增时传null）
     * @return 是否唯一
     */
    @Override
    public boolean isNameUnique(String name, Integer excludeId) {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("单位名称不能为空");
        }

        log.info("检查单位名称唯一性 - 名称:{}, 排除ID:{}", name, excludeId);

        TOrganizationInfo existing = organizationInfoMapper.selectByNameExcludeId(name.trim(), excludeId);
        boolean isUnique = existing == null;

        log.info("单位名称唯一性检查结果 - 名称:{}, 是否唯一:{}", name, isUnique);
        return isUnique;
    }

    /**
     * 检查单位名称唯一性（简化版本）
     * @param name 单位名称
     */
    @Override
    public void checkNameUnique(String name) {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("单位名称不能为空");
        }

        log.info("检查单位名称唯一性 - 名称:{}", name);

        TOrganizationInfo existing = organizationInfoMapper.selectByName(name.trim());
        if (existing != null) {
            log.warn("单位名称已存在 - 名称:{}, 已存在ID:{}", name, existing.getId());
            throw new ValidationException.DuplicateDataException("单位名称", name);
        }

        log.info("单位名称唯一性检查通过 - 名称:{}", name);
    }
}
