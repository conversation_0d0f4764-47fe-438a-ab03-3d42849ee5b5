package com.lysjk.service;

import com.lysjk.dto.TOrganizationInfoPageQueryDTO;
import com.lysjk.entity.TOrganizationInfo;
import com.lysjk.result.PageResult;

import java.util.List;

public interface TOrganizationInfoService {
    /**
     * 新增单位信息
     * @param organizationInfo
     */
    void save(TOrganizationInfo organizationInfo);

    /**
     * 批量删除操作
     * @param ids
     */
    void deleteBath(List<Long> ids);

    /**
     * 更新单位信息
     * @param organizationInfo
     */
    void update(TOrganizationInfo organizationInfo);

    /**
     * 根据名称条件分页查询
     * @param organizationInfoPageQueryDTO
     * @return
     */
    PageResult selectPage(TOrganizationInfoPageQueryDTO organizationInfoPageQueryDTO);

    /**
     * 检查单位名称唯一性
     * @param name 单位名称
     * @param excludeId 排除的ID（更新时使用，新增时传null）
     * @return 是否唯一
     */
    boolean isNameUnique(String name, Integer excludeId);
}
