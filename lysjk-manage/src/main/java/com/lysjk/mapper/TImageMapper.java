package com.lysjk.mapper;

import com.github.pagehelper.Page;
import com.lysjk.anno.AutoFill;
import com.lysjk.dto.TImagePageQueryDTO;
import com.lysjk.entity.TImage;
import com.lysjk.enums.Operation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 影像信息表Mapper接口
 */
@Mapper
public interface TImageMapper {

    // 基础CRUD操作

    /**
     * 根据主键删除影像信息
     * @param id 影像ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * 批量删除影像信息
     * @param ids ID列表
     * @return 影响行数
     */
    int deleteByIds(List<Integer> ids);

    /**
     * 新增影像信息
     * @param record 影像信息
     * @return 影响行数
     */
    @AutoFill(Operation.INSERT)
    int insert(TImage record);

    /**
     * 选择性新增影像信息
     * @param record 影像信息
     * @return 影响行数
     */
    @AutoFill(Operation.INSERT)
    int insertSelective(TImage record);

    /**
     * 根据主键查询影像信息
     * @param id 影像ID
     * @return 影像信息
     */
    TImage selectByPrimaryKey(Integer id);

    /**
     * 根据主键更新影像信息
     * @param record 影像信息
     * @return 影响行数
     */
    @AutoFill(Operation.UPDATE)
    int updateByPrimaryKey(TImage record);

    // 扩展的业务方法

    /**
     * 更新业务字段（排除系统字段）
     * @param record 影像信息对象
     * @return 影响行数
     */
    @AutoFill(Operation.UPDATE)
    int updateBusinessFields(TImage record);

    /**
     * 分页条件查询影像信息
     * @param pageQueryDTO 分页查询条件
     * @return 分页结果
     */
    Page<TImage> pageQuery(TImagePageQueryDTO pageQueryDTO);

    /**
     * 查询所有影像信息（用于分页）
     * @return 影像信息列表
     */
    List<TImage> selectAll();

    /**
     * 根据地物ID查询影像信息列表
     * @param regionId 地物ID
     * @return 影像信息列表
     */
    List<TImage> selectByRegionId(@Param("regionId") Integer regionId);

    /**
     * 根据传感器类型查询影像信息列表
     * @param sensorType 传感器类型
     * @return 影像信息列表
     */
    List<TImage> selectBySensorType(@Param("sensorType") String sensorType);

    /**
     * 根据影像名称查询影像信息（用于唯一性检查）
     * @param name 影像名称
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 影像信息
     */
    TImage selectByNameExcludeId(@Param("name") String name, @Param("excludeId") Integer excludeId);
}
