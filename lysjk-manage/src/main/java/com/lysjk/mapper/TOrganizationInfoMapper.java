package com.lysjk.mapper;

import com.github.pagehelper.Page;
import com.lysjk.anno.AutoFill;
import com.lysjk.dto.TOrganizationInfoPageQueryDTO;
import com.lysjk.entity.TOrganizationInfo;
import com.lysjk.enums.Operation;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface TOrganizationInfoMapper {
    /**
     * 新增单位信息
     * remark非必须,其余必须
     * @param organizationInfo
     */
    @AutoFill(Operation.INSERT)
    @Insert("insert into t_organization_info (name, create_dt, create_by, update_dt, update_by, remark) " +
            "values (#{name}, #{createDt}, #{createBy}, #{updateDt}, #{updateBy}, #{remark})")
    void insert(TOrganizationInfo organizationInfo);

    /**
     * 批量删除单位信息
     * @param ids
     */
    void deleteByIds(List<Long> ids);

    /**
     * 更新单位信息
     * @param organizationInfo
     */
    @AutoFill(Operation.UPDATE)
    @Update("update t_organization_info set name = #{name}, remark = #{remark}, update_dt = #{updateDt}, update_by = #{updateBy} where id = #{id}")
    void update(TOrganizationInfo organizationInfo);

    /**
     * 分页查询都用到了动态sql,所以直接写在映射文件中
     * @param organizationInfoPageQueryDTO
     * @return
     */
    Page<TOrganizationInfo> pageQuery(TOrganizationInfoPageQueryDTO organizationInfoPageQueryDTO);

    /**
     * 根据名称查询单位信息（排除指定ID）
     * 用于检查名称唯一性
     * @param name 单位名称
     * @param excludeId 排除的ID（更新时使用，新增时传null）
     * @return 单位信息，不存在返回null
     */
    @Select("SELECT * FROM t_organization_info WHERE name = #{name} " +
            "AND (#{excludeId} IS NULL OR id != #{excludeId}) LIMIT 1")
    TOrganizationInfo selectByNameExcludeId(@Param("name") String name, @Param("excludeId") Integer excludeId);

    /**
     * 根据名称查询单位信息
     * 用于简化的名称唯一性检查
     * @param name 单位名称
     * @return 单位信息，不存在返回null
     */
    @Select("SELECT * FROM t_organization_info WHERE name = #{name} LIMIT 1")
    TOrganizationInfo selectByName(@Param("name") String name);
}
