package com.lysjk.config;

import com.lysjk.config.typehandler.MultiPolygonTypeHandler;
import com.lysjk.config.typehandler.PointTypeHandler;
import com.lysjk.config.typehandler.PolygonTypeHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.locationtech.jts.geom.MultiPolygon;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.Polygon;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 启动检查类
 * 验证PostGIS和TypeHandler配置是否正确
 */
@Slf4j
@Component
@Order(100) // 确保在其他组件之后执行
public class StartupCheck implements CommandLineRunner {

    @Autowired(required = false)
    private SqlSessionFactory sqlSessionFactory;

    @Override
    public void run(String... args) throws Exception {
        log.info("开始启动检查...");

        // 检查TypeHandler注册情况
        if (sqlSessionFactory != null) {
            checkTypeHandlers();
        } else {
            log.warn("SqlSessionFactory未找到，跳过TypeHandler检查");
        }

        log.info("启动检查完成");
    }

    private void checkTypeHandlers() {
        try {
            TypeHandlerRegistry registry = sqlSessionFactory.getConfiguration().getTypeHandlerRegistry();

            // 检查Point TypeHandler
            var pointHandler = registry.getTypeHandler(Point.class);
            if (pointHandler instanceof PointTypeHandler) {
                log.info("✓ Point TypeHandler 注册成功");
            } else {
                log.warn("✗ Point TypeHandler 注册失败，当前类型: {}",
                        pointHandler != null ? pointHandler.getClass().getSimpleName() : "null");
            }

            // 检查MultiPolygon TypeHandler
            var multiPolygonHandler = registry.getTypeHandler(MultiPolygon.class);
            if (multiPolygonHandler instanceof MultiPolygonTypeHandler) {
                log.info("✓ MultiPolygon TypeHandler 注册成功");
            } else {
                log.warn("✗ MultiPolygon TypeHandler 注册失败，当前类型: {}",
                        multiPolygonHandler != null ? multiPolygonHandler.getClass().getSimpleName() : "null");
            }

            // 检查Polygon TypeHandler
            var polygonHandler = registry.getTypeHandler(Polygon.class);
            if (polygonHandler instanceof PolygonTypeHandler) {
                log.info("✓ Polygon TypeHandler 注册成功");
            } else {
                log.warn("✗ Polygon TypeHandler 注册失败，当前类型: {}",
                        polygonHandler != null ? polygonHandler.getClass().getSimpleName() : "null");
            }

            // 输出所有已注册的TypeHandler数量（调试用）
            log.debug("已注册的TypeHandler数量: {}", registry.getTypeHandlers().size());

        } catch (Exception e) {
            log.error("TypeHandler检查失败", e);
        }
    }
}

//package com.lysjk.config;
//
//import com.lysjk.config.typehandler.MultiPolygonTypeHandler;
//import com.lysjk.config.typehandler.PointTypeHandler;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.ibatis.session.SqlSessionFactory;
//import org.apache.ibatis.type.TypeHandlerRegistry;
//import org.locationtech.jts.geom.MultiPolygon;
//import org.locationtech.jts.geom.Point;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.CommandLineRunner;
//import org.springframework.core.annotation.Order;
//import org.springframework.stereotype.Component;
//
///**
// * 启动检查类
// * 验证PostGIS和TypeHandler配置是否正确
// */
//@Slf4j
//@Component
//@Order(100) // 确保在其他组件之后执行
//public class StartupCheck implements CommandLineRunner {
//
//    @Autowired(required = false)
//    private SqlSessionFactory sqlSessionFactory;
//
//    @Override
//    public void run(String... args) throws Exception {
//        log.info("开始启动检查...");
//
//        // 检查TypeHandler注册情况
//        if (sqlSessionFactory != null) {
//            checkTypeHandlers();
//        } else {
//            log.warn("SqlSessionFactory未找到，跳过TypeHandler检查");
//        }
//
//        log.info("启动检查完成");
//    }
//
//    private void checkTypeHandlers() {
//        try {
//            TypeHandlerRegistry registry = sqlSessionFactory.getConfiguration().getTypeHandlerRegistry();
//
//            // 检查Point TypeHandler
//            var pointHandler = registry.getTypeHandler(Point.class);
//            if (pointHandler instanceof PointTypeHandler) {
//                log.info("✓ Point TypeHandler 注册成功");
//            } else {
//                log.warn("✗ Point TypeHandler 注册失败，当前类型: {}",
//                    pointHandler != null ? pointHandler.getClass().getSimpleName() : "null");
//            }
//
//            // 检查MultiPolygon TypeHandler
//            var multiPolygonHandler = registry.getTypeHandler(MultiPolygon.class);
//            if (multiPolygonHandler instanceof MultiPolygonTypeHandler) {
//                log.info("✓ MultiPolygon TypeHandler 注册成功");
//            } else {
//                log.warn("✗ MultiPolygon TypeHandler 注册失败，当前类型: {}",
//                    multiPolygonHandler != null ? multiPolygonHandler.getClass().getSimpleName() : "null");
//            }
//
//            // 输出所有已注册的TypeHandler
//            log.debug("已注册的TypeHandler数量: {}", registry.getTypeHandlers().size());
//
//        } catch (Exception e) {
//            log.error("TypeHandler检查失败", e);
//        }
//    }
//}
