package com.lysjk.config.typehandler;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;
import org.locationtech.jts.geom.Polygon;
import org.locationtech.jts.io.ParseException;
import org.locationtech.jts.io.WKBReader;
import org.locationtech.jts.io.WKTReader;
import org.locationtech.jts.io.WKTWriter;
import org.postgresql.util.PGobject;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * PostGIS Polygon类型处理器
 * 处理Java Polygon对象与PostgreSQL geometry类型之间的转换
 */
@Slf4j
@MappedTypes(Polygon.class)
public class PolygonTypeHandler extends BaseTypeHandler<Polygon> {

    private static final WKTReader WKT_READER = new WKTReader();
    private static final WKTWriter WKT_WRITER = new WKTWriter();
    private static final WKBReader WKB_READER = new WKBReader();

    private static final int SRID = 4326; // 可配置的SRID

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Polygon parameter, JdbcType jdbcType) throws SQLException {
        try {
            // 将Polygon对象转换为PostGIS可识别的格式
            PGobject pGobject = new PGobject();
            pGobject.setType("geometry");

            if (parameter != null) {
                // 使用WKT格式，并指定SRID
                String wkt = WKT_WRITER.write(parameter);
                pGobject.setValue("SRID=" + SRID + ";" + wkt);
            } else {
                pGobject.setValue(null);
            }

            ps.setObject(i, pGobject);

            if (log.isDebugEnabled()) {
                log.debug("设置Polygon参数: {}", WKT_WRITER.write(parameter));
            }

        } catch (Exception e) {
            log.error("设置Polygon参数失败：{}", parameter, e);
            throw new SQLException("设置Polygon参数失败，数据：" + parameter, e);
        }
    }

    @Override
    public Polygon getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return getPolygon(rs.getObject(columnName));
    }

    @Override
    public Polygon getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return getPolygon(rs.getObject(columnIndex));
    }

    @Override
    public Polygon getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return getPolygon(cs.getObject(columnIndex));
    }

    /**
     * 从数据库对象转换为Polygon
     */
    private Polygon getPolygon(Object object) throws SQLException {
        if (object == null) {
            return null;
        }

        try {
            org.locationtech.jts.geom.Geometry geometry = parseGeometry(object);

            if (geometry instanceof Polygon) {
                return (Polygon) geometry;
            } else {
                throw new SQLException("几何对象不是Polygon类型: " + geometry.getGeometryType());
            }
        } catch (Exception e) {
            log.error("转换Polygon失败: {}", object, e);
            throw new SQLException("无法转换Polygon数据", e);
        }
    }

    private org.locationtech.jts.geom.Geometry parseGeometry(Object object) throws SQLException {
        try {
            org.locationtech.jts.geom.Geometry geometry = null;

            if (object instanceof PGobject) {
                String value = ((PGobject) object).getValue();
                geometry = parseWKT(value);
            } else if (object instanceof String) {
                geometry = parseWKT((String) object);
            } else if (object instanceof byte[]) {
                geometry = WKB_READER.read((byte[]) object);
            } else {
                log.warn("未知的Polygon数据类型: {}", object.getClass().getName());
                return null;
            }

            return geometry;
        } catch (Exception e) {
            log.error("解析几何对象失败: {}", object, e);
            throw new SQLException("无法解析几何数据", e);
        }
    }

    private org.locationtech.jts.geom.Geometry parseWKT(String wkt) throws ParseException {
        if (wkt != null && wkt.startsWith("SRID=")) {
            int semicolonIndex = wkt.indexOf(';');
            wkt = wkt.substring(semicolonIndex + 1);
        }
        return WKT_READER.read(wkt);
    }
}
