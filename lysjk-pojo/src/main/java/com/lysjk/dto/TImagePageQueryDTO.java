package com.lysjk.dto;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 影像信息分页查询DTO
 */
@Data
public class TImagePageQueryDTO implements Serializable {

    //页码(先默认设值)
    private int page = 1;

    //每页显示记录数
    private int pageSize = 10;

    /**
     * 影像名称
     */
    private String name;

    /**
     * 传感器类型
     */
    private String sensorType;

    /**
     * 所属地物ID
     */
    private Integer regionId;

    /**
     * 影像获取时间开始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime acquisitionDtStart;

    /**
     * 影像获取时间结束
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime acquisitionDtEnd;

    /**
     * 记录创建时间开始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDtStart;

    /**
     * 记录创建时间结束
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDtEnd;

    /**
     * 记录创建者ID
     */
    private Integer createBy;

    /**
     * 备注
     */
    private String remark;

    private static final long serialVersionUID = 1L;
}
