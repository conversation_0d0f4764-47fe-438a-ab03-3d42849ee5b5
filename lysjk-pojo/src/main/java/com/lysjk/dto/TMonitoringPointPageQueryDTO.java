package com.lysjk.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 监测点信息分页查询DTO
 */
@Data
public class TMonitoringPointPageQueryDTO implements Serializable {

    //页码(先默认设值)
    private int page = 1;

    //每页显示记录数
    private int pageSize = 10;

    /**
     * 监测点编码
     */
    private String code;

    /**
     * 监测点名称
     */
    private String name;

    /**
     * 地物ID
     */
    private Integer regionId;

    /**
     * 备注
     */
    private String remark;

    private static final long serialVersionUID = 1L;
}
